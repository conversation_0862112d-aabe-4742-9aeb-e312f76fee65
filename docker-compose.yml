cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  litellm-proxy:
    build: .
    container_name: litellm-proxy-gateway
    ports:
      - "8000:8000"  # Map host port 8000 to container port 8000
    env_file:
      - .env  # Load environment variables from .env file
    restart: always  # Restart if container crashes
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
EOF