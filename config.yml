cat > config.yaml << 'EOF'
# LiteLLM Configuration for Azure Proxy Gateway
model_list:
  - model_name: "azure-chatgpt"  # Alias for internal tools
    litellm_params:
      model: "azure/gpt-4o"  # Azure model deployment
      api_key: os.environ/AZURE_COMPANY_USER_TOKEN
      api_base: os.environ/AZURE_COMPANY_PROXY_URL
      # Uncomment if your Azure proxy requires specific API version:
      # api_version: "2024-02-15"
      # Uncomment if using specific Azure deployment ID:
      # azure_deployment: "gpt-4o-deployment"

# Optional: Default model if not specified by clients
# default_model: "azure-chatgpt"

# Optional: Logging and monitoring callbacks
# success_callback: ["langfuse"]
# failure_callback: ["sentry"]
EOF