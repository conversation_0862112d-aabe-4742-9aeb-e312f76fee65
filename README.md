cat > README.md << 'EOF'

\# LLM Proxy Gateway



OpenAI API Proxy/Gateway using LiteLLM to connect open-source tools to company's Azure-hosted ChatGPT models.



\## Quick Start



1\. \*\*Configure environment variables in `.env`\*\*

2\. \*\*Start the proxy:\*\* `docker-compose up -d`

3\. \*\*Test:\*\* `python test\_client.py`



\## Usage



```python

from openai import OpenAI



client = OpenAI(

&nbsp;   base\_url="http://localhost:8000",

&nbsp;   api\_key="your-internal-key"

)



response = client.chat.completions.create(

&nbsp;   model="azure-chatgpt",

&nbsp;   messages=\[{"role": "user", "content": "Hello!"}]

)

