ToDo List: OpenAI API Proxy/Gateway Implementation

This document provides a detailed, step-by-step guide for a junior developer to implement the OpenAI API Proxy/Gateway using LiteLLM, as defined in the Product Requirements Document (PRD).



Phase 1: Project Setup \& Initial LiteLLM Configuration

Task 1.1: Set Up Development Environment

Objective: Prepare your local machine for development.



Steps:



Install Python: Ensure Python 3.8+ is installed. Download from python.org.



Install pip: Python's package installer (usually comes with Python).



Install Docker \& Docker Compose: Download and install Docker Desktop (includes Docker Compose) from docker.com.



Choose an IDE: Select a suitable Integrated Development Environment (IDE) like VS Code, PyCharm, or Atom.



Create Project Directory: Create a new folder for your project (e.g., llm-proxy-gateway).



mkdir llm-proxy-gateway

cd llm-proxy-gateway



Task 1.2: Install LiteLLM

Objective: Get LiteLLM installed and ready.



Steps:



Create a Virtual Environment (Best Practice): Isolate project dependencies.



python -m venv venv

\# On Windows:

\# .\\venv\\Scripts\\activate

\# On macOS/Linux:

source venv/bin/activate



Install LiteLLM:



pip install litellm



Verify Installation:



litellm --version



Task 1.3: Configure LiteLLM for Azure Proxy

Objective: Tell LiteLLM how to connect to your company's Azure proxy service.



Steps:



Create config.yaml: In your project root (llm-proxy-gateway/), create a file named config.yaml.



Add Azure Proxy Configuration: Populate config.yaml with the following, replacing placeholders with your actual values.



AZURE\_COMPANY\_PROXY\_URL: This is the specific URL of your company's Azure proxy service (e.g., https://your-company-azure-proxy.com/openai/deployments/gpt-4o/chat/completions). Consult your internal documentation or team for this exact URL.



AZURE\_COMPANY\_USER\_TOKEN: This is your company's proprietary user token for accessing the Azure proxy. This is a sensitive value and should be handled securely.



\# config.yaml

model\_list:

&nbsp; - model\_name: "azure-chatgpt" # This is the alias your internal tools will use

&nbsp;   litellm\_params:

&nbsp;     model: "azure/gpt-4o" # The actual model name/deployment ID in Azure

&nbsp;     api\_key: os.environ/AZURE\_COMPANY\_USER\_TOKEN # LiteLLM reads from env var

&nbsp;     api\_base: os.environ/AZURE\_COMPANY\_PROXY\_URL # LiteLLM reads from env var

&nbsp;     # If your Azure proxy requires a specific API version, add it here:

&nbsp;     # api\_version: "2024-02-15" # Example Azure API version

&nbsp;     # If your Azure proxy uses a specific Azure deployment ID:

&nbsp;     # azure\_deployment: "gpt-4o-deployment" # Example deployment name



\# Optional: Set a default model if not specified by clients

\# default\_model: "azure-chatgpt"



\# Optional: Set up logging if not handled by Docker/external tools

\# success\_callback: \["langfuse"] # Example: if you use Langfuse for tracing

\# failure\_callback: \["sentry"] # Example: if you use Sentry for error reporting



Create .env file (for local testing): In your project root, create a .env file to store sensitive environment variables for local development. DO NOT commit this file to version control (e.g., Git).



\# .env

AZURE\_COMPANY\_PROXY\_URL="https://your-company-azure-proxy.com/openai/deployments/gpt-4o/chat/completions"

AZURE\_COMPANY\_USER\_TOKEN="your\_actual\_proprietary\_user\_token\_here"



Load Environment Variables (for local testing): When running LiteLLM directly, you'll need to load these. For Docker, it's handled by Docker Compose.



\# For local testing, install python-dotenv

pip install python-dotenv

\# In a Python script or your shell before running litellm:

\# import os

\# from dotenv import load\_dotenv

\# load\_dotenv() # This loads variables from .env



Phase 2: Dockerization

Task 2.1: Create Dockerfile

Objective: Define how to build your LiteLLM proxy into a Docker image.



Steps:



Create Dockerfile: In your project root, create a file named Dockerfile.



Add Dockerfile Content:



\# Dockerfile

\# Use an official Python runtime as a parent image

FROM python:3.9-slim-buster



\# Set the working directory in the container

WORKDIR /app



\# Copy the LiteLLM configuration file into the container

COPY config.yaml /app/config.yaml



\# Install LiteLLM and other necessary packages

RUN pip install --no-cache-dir litellm python-dotenv # python-dotenv for local testing, not strictly needed for LiteLLM server itself if env vars are passed directly



\# Expose the port LiteLLM will run on

EXPOSE 8000



\# Command to run the LiteLLM proxy server

\# LiteLLM will automatically pick up config.yaml if present in /app

\# --host 0.0.0.0 makes it accessible from outside the container

CMD \["litellm", "--config", "config.yaml", "--host", "0.0.0.0"]



Task 2.2: Create Docker Compose File

Objective: Define how to run your LiteLLM proxy service using Docker Compose, including environment variables.



Steps:



Create docker-compose.yml: In your project root, create a file named docker-compose.yml.



Add Docker Compose Content:



\# docker-compose.yml

version: '3.8'



services:

&nbsp; litellm-proxy:

&nbsp;   build: . # Build from the current directory (where Dockerfile is)

&nbsp;   container\_name: litellm-proxy-gateway

&nbsp;   ports:

&nbsp;     - "8000:8000" # Map host port 8000 to container port 8000

&nbsp;   env\_file:

&nbsp;     - .env # Load environment variables from .env file

&nbsp;   restart: always # Ensure the service restarts if it crashes



Phase 3: Deployment \& Testing

Task 3.1: Build and Run the Docker Container

Objective: Get your LiteLLM proxy running.



Steps:



Build the Docker Image:



docker-compose build



Run the Container:



docker-compose up -d # -d runs in detached mode (background)



Verify Container Status:



docker-compose ps

\# You should see 'litellm-proxy-gateway' with status 'Up'



View Container Logs (for debugging):



docker-compose logs -f litellm-proxy-gateway



Task 3.2: Test the Proxy with a Sample OpenAI Client

Objective: Confirm that the proxy is correctly forwarding requests to your Azure service.



Steps:



Create a Test Python Script: In your project root, create test\_client.py.



\# test\_client.py

import os

from openai import OpenAI

from dotenv import load\_dotenv



\# Load environment variables from .env for local script execution

load\_dotenv()



\# Configure the OpenAI client to point to your LiteLLM proxy

\# The API key here is what your open-source tools would pass.

\# If LiteLLM is configured to simply pass it through, this can be anything.

\# If LiteLLM validates it, you'd put a LiteLLM-specific key here.

client = OpenAI(

&nbsp;   base\_url="http://localhost:8000",  # Your LiteLLM proxy URL

&nbsp;   api\_key="sk-your-internal-key", # This can be any string, as LiteLLM will use the configured AZURE\_COMPANY\_USER\_TOKEN

)



try:

&nbsp;   print("Sending request to LiteLLM proxy...")

&nbsp;   chat\_completion = client.chat.completions.create(

&nbsp;       model="azure-chatgpt", # Use the model\_name alias defined in config.yaml

&nbsp;       messages=\[

&nbsp;           {"role": "system", "content": "You are a helpful assistant."},

&nbsp;           {"role": "user", "content": "What is the capital of France?"}

&nbsp;       ],

&nbsp;       temperature=0.7,

&nbsp;       max\_tokens=50

&nbsp;   )

&nbsp;   print("\\nResponse from LLM:")

&nbsp;   print(chat\_completion.choices\[0].message.content)



except Exception as e:

&nbsp;   print(f"An error occurred: {e}")

&nbsp;   # Check container logs for more details: docker-compose logs -f litellm-proxy-gateway

&nbsp;   # Verify your AZURE\_COMPANY\_PROXY\_URL and AZURE\_COMPANY\_USER\_TOKEN in .env and config.yaml

&nbsp;   # Ensure the model name 'azure-chatgpt' matches your config.yaml



Install OpenAI Python Library:



pip install openai



Run the Test Script:



python test\_client.py



Verify Output: Check if you receive a correct response from the LLM via the proxy. If not, check the docker-compose logs -f litellm-proxy-gateway for errors.



Phase 4: Logging, Monitoring \& Security

Task 4.1: Implement Basic Health Check

Objective: Ensure the proxy has a simple way to report its operational status.



Steps:



LiteLLM automatically provides a health check endpoint at http://localhost:8000/health.



Test Health Check:



curl http://localhost:8000/health

\# Expected output: {"status":"ok"}



Task 4.2: Review Logging Configuration

Objective: Ensure logs provide sufficient information for debugging and auditing.



Steps:



LiteLLM logs to standard output by default, which Docker captures.



Review docker-compose logs: Regularly check the logs (docker-compose logs -f litellm-proxy-gateway) during testing to understand the proxy's behavior and identify any errors.



Discuss Centralized Logging: For production, discuss with your System Administrator/DevOps team how to integrate Docker logs with your company's centralized logging solution (e.g., ELK stack, Splunk, CloudWatch Logs).



Task 4.3: Secure User Token

Objective: Ensure the company's proprietary user token is never exposed.



Steps:



Environment Variables ONLY: Confirm that AZURE\_COMPANY\_USER\_TOKEN is only passed to the Docker container via environment variables (e.g., env\_file in Docker Compose or Kubernetes secrets). NEVER hardcode it in any script or configuration file that is committed to version control.



.env File Exclusion: Ensure your .env file is in your .gitignore to prevent accidental commits.



Access Control (Future Discussion): For production, discuss with the Security Team if additional access control (e.g., IP whitelisting, specific API keys for the LiteLLM proxy itself) is required beyond the Azure proxy's authentication.



Phase 5: Documentation \& Handover

Task 5.1: Document the Implementation

Objective: Provide clear documentation for future maintenance and new developers.



Steps:



Create a README.md file: In your project root.



Include:



Project overview and purpose.



Setup instructions (prerequisites, cloning repo, docker-compose up).



Configuration details (config.yaml, environment variables).



How to test the proxy.



Common troubleshooting steps.



How to access logs.



Security considerations (especially token handling).



Task 5.2: Prepare for Production Deployment

Objective: Ensure the proxy is ready for deployment to a production environment.



Steps:



Coordinate with DevOps: Work with your System Administrator/DevOps team to plan the production deployment.



Review Scalability: Discuss how to deploy multiple instances behind a load balancer (as per NFR-010).



Monitoring Integration: Confirm integration with production monitoring tools (Prometheus, Grafana, etc.).



Secret Management: Discuss how AZURE\_COMPANY\_USER\_TOKEN will be managed in production (e.g., Kubernetes Secrets, Azure Key Vault, AWS Secrets Manager).



By following these steps, a junior developer can successfully implement and deploy the OpenAI API Proxy/Gateway, enabling broader compatibility for open-source LLM tools within your company's existing Azure infrastructure.

