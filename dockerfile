cat > Dockerfile << 'EOF'
# Use official Python runtime as parent image
FROM python:3.9-slim-buster

# Set working directory in container
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Copy LiteLLM configuration file
COPY config.yaml /app/config.yaml

# Expose port LiteLLM will run on
EXPOSE 8000

# Command to run LiteLLM proxy server
# --host 0.0.0.0 makes it accessible from outside container
CMD ["litellm", "--config", "config.yaml", "--host", "0.0.0.0", "--port", "8000"]
EOF